// 存储配额管理器
// 专门管理Chrome扩展的存储配额和数据清理

class StorageQuotaManager {
  constructor() {
    this.SYNC_QUOTA_BYTES = 102400; // 100KB sync storage限制
    this.SYNC_ITEM_QUOTA_BYTES = 8192; // 8KB单项限制
    this.LOCAL_QUOTA_BYTES = 5242880; // 5MB local storage限制
    
    this.WARNING_THRESHOLD = 0.8; // 80%使用率警告
    this.CLEANUP_THRESHOLD = 0.9; // 90%使用率自动清理
  }

  // 检查存储使用情况
  async checkStorageUsage() {
    try {
      const syncUsage = await this.getSyncStorageUsage();
      const localUsage = await this.getLocalStorageUsage();
      
      return {
        sync: {
          used: syncUsage.used,
          total: this.SYNC_QUOTA_BYTES,
          percentage: syncUsage.used / this.SYNC_QUOTA_BYTES,
          needsCleanup: syncUsage.used / this.SYNC_QUOTA_BYTES > this.CLEANUP_THRESHOLD
        },
        local: {
          used: localUsage.used,
          total: this.LOCAL_QUOTA_BYTES,
          percentage: localUsage.used / this.LOCAL_QUOTA_BYTES,
          needsCleanup: localUsage.used / this.LOCAL_QUOTA_BYTES > this.CLEANUP_THRESHOLD
        }
      };
    } catch (error) {
      console.error('检查存储使用情况失败:', error);
      return null;
    }
  }

  // 获取sync storage使用情况
  async getSyncStorageUsage() {
    return new Promise((resolve, reject) => {
      chrome.storage.sync.getBytesInUse(null, (bytesInUse) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve({ used: bytesInUse });
        }
      });
    });
  }

  // 获取local storage使用情况
  async getLocalStorageUsage() {
    return new Promise((resolve, reject) => {
      chrome.storage.local.getBytesInUse(null, (bytesInUse) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve({ used: bytesInUse });
        }
      });
    });
  }

  // 检查单个项目大小
  checkItemSize(data) {
    const size = JSON.stringify(data).length;
    return {
      size: size,
      exceedsLimit: size > this.SYNC_ITEM_QUOTA_BYTES,
      percentage: size / this.SYNC_ITEM_QUOTA_BYTES
    };
  }

  // 压缩数据
  compressData(data) {
    if (typeof data === 'string') {
      // 简单的字符串压缩
      return data.replace(/\s+/g, ' ').trim();
    } else if (typeof data === 'object') {
      const compressed = { ...data };
      
      // 压缩常见的大字段
      if (compressed.content && compressed.content.length > 3000) {
        compressed.content = compressed.content.substring(0, 3000) + '...[已压缩]';
      }
      
      if (compressed.summary && compressed.summary.length > 1000) {
        compressed.summary = compressed.summary.substring(0, 1000) + '...[已压缩]';
      }
      
      // 移除不必要的字段
      delete compressed.rawContent;
      delete compressed.metadata;
      delete compressed.debugInfo;
      
      return compressed;
    }
    
    return data;
  }

  // 自动清理存储
  async autoCleanup() {
    try {
      const usage = await this.checkStorageUsage();
      
      if (!usage) {
        return { success: false, error: '无法检查存储使用情况' };
      }
      
      let cleanedItems = 0;
      
      // 清理sync storage
      if (usage.sync.needsCleanup) {
        const syncCleaned = await this.cleanupSyncStorage();
        cleanedItems += syncCleaned;
      }
      
      // 清理local storage
      if (usage.local.needsCleanup) {
        const localCleaned = await this.cleanupLocalStorage();
        cleanedItems += localCleaned;
      }
      
      return {
        success: true,
        cleanedItems: cleanedItems,
        usage: await this.checkStorageUsage()
      };
    } catch (error) {
      console.error('自动清理失败:', error);
      return { success: false, error: error.message };
    }
  }

  // 清理sync storage
  async cleanupSyncStorage() {
    try {
      const data = await chrome.storage.sync.get();
      let cleanedItems = 0;
      
      // 清理历史记录（如果还在sync中）
      if (data.history && Array.isArray(data.history)) {
        const oldLength = data.history.length;
        data.history = data.history.slice(0, 10); // 只保留最新10条
        cleanedItems += oldLength - data.history.length;
        await chrome.storage.sync.set({ history: data.history });
      }
      
      // 清理过期的临时数据
      const keysToRemove = [];
      for (const key in data) {
        if (key.startsWith('temp_') || key.startsWith('cache_')) {
          keysToRemove.push(key);
        }
      }
      
      if (keysToRemove.length > 0) {
        await chrome.storage.sync.remove(keysToRemove);
        cleanedItems += keysToRemove.length;
      }
      
      return cleanedItems;
    } catch (error) {
      console.error('清理sync storage失败:', error);
      return 0;
    }
  }

  // 清理local storage
  async cleanupLocalStorage() {
    try {
      const data = await chrome.storage.local.get();
      let cleanedItems = 0;
      
      // 清理历史记录
      if (data.history && Array.isArray(data.history)) {
        const oneWeekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
        const oldLength = data.history.length;
        
        data.history = data.history
          .filter(record => {
            const recordTime = new Date(record.timestamp).getTime();
            return recordTime > oneWeekAgo;
          })
          .slice(0, 30) // 最多保留30条
          .map(record => this.compressData(record));
        
        cleanedItems += oldLength - data.history.length;
        await chrome.storage.local.set({ history: data.history });
      }
      
      // 清理过期的临时数据
      const keysToRemove = [];
      for (const key in data) {
        if (key.startsWith('temp_') || key.startsWith('cache_')) {
          keysToRemove.push(key);
        }
      }
      
      if (keysToRemove.length > 0) {
        await chrome.storage.local.remove(keysToRemove);
        cleanedItems += keysToRemove.length;
      }
      
      return cleanedItems;
    } catch (error) {
      console.error('清理local storage失败:', error);
      return 0;
    }
  }

  // 获取存储建议
  async getStorageRecommendations() {
    const usage = await this.checkStorageUsage();
    const recommendations = [];
    
    if (!usage) {
      return ['无法检查存储使用情况'];
    }
    
    if (usage.sync.percentage > this.WARNING_THRESHOLD) {
      recommendations.push('同步存储使用率较高，建议清理配置数据');
    }
    
    if (usage.local.percentage > this.WARNING_THRESHOLD) {
      recommendations.push('本地存储使用率较高，建议清理历史记录');
    }
    
    if (usage.sync.needsCleanup || usage.local.needsCleanup) {
      recommendations.push('建议立即执行存储清理');
    }
    
    if (recommendations.length === 0) {
      recommendations.push('存储使用情况良好');
    }
    
    return recommendations;
  }

  // 监控存储使用情况
  startMonitoring() {
    // 每小时检查一次存储使用情况
    setInterval(async () => {
      const usage = await this.checkStorageUsage();
      if (usage && (usage.sync.needsCleanup || usage.local.needsCleanup)) {
        console.warn('存储使用率过高，建议清理');
        await this.autoCleanup();
      }
    }, 60 * 60 * 1000); // 1小时
  }
}

// 导出单例
const storageQuotaManager = new StorageQuotaManager();

// 如果在Chrome扩展环境中，自动开始监控
if (typeof chrome !== 'undefined' && chrome.storage) {
  storageQuotaManager.startMonitoring();
}
