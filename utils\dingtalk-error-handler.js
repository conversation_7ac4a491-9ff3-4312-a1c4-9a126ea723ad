// 钉钉认证错误处理器
// 处理钉钉认证过程中的各种错误情况

// 错误类型定义
const DINGTALK_ERROR_TYPES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  AUTH_EXPIRED: 'AUTH_EXPIRED',
  PERMISSION_DENIED: 'PERMISSION_DENIED', 
  ORG_NOT_SELECTED: 'ORG_NOT_SELECTED',
  API_ERROR: 'API_ERROR',
  COOKIE_ERROR: 'COOKIE_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
};

// 消息类型定义
const DINGTALK_MESSAGE_TYPES = {
  // Background → Sidebar/Content
  DT_AUTH_STATUS_CHANGED: 'DT_AUTH_STATUS_CHANGED',
  DT_LOGIN_REQUIRED: 'DT_LOGIN_REQUIRED',
  DT_ORG_SELECTION_REQUIRED: 'DT_ORG_SELECTION_REQUIRED',
  DT_AUTH_ERROR: 'DT_AUTH_ERROR',
  DT_NETWORK_ERROR: 'DT_NETWORK_ERROR',
  DT_PERMISSION_ERROR: 'DT_PERMISSION_ERROR',
  
  // Sidebar/Content → Background
  DT_INITIATE_LOGIN: 'DT_INITIATE_LOGIN',
  DT_GET_AUTH_STATUS: 'DT_GET_AUTH_STATUS',
  DT_SELECT_ORGANIZATION: 'DT_SELECT_ORGANIZATION',
  DT_LOGOUT: 'DT_LOGOUT',
  DT_REFRESH_AUTH: 'DT_REFRESH_AUTH'
};

class DingTalkErrorHandler {
  constructor() {
    this.errorHistory = [];
    this.maxHistorySize = 50;
  }

  // 分析错误类型
  analyzeError(error) {
    console.log('分析钉钉认证错误:', error);
    
    // 记录错误历史
    this.recordError(error);
    
    // 网络连接错误
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      return {
        type: DINGTALK_ERROR_TYPES.NETWORK_ERROR,
        message: '网络连接失败，请检查网络设置',
        suggestion: '请检查网络连接后重试',
        retry: true,
        icon: this.getErrorIcon('network')
      };
    }
    
    // HTTP状态码错误
    if (error.message && error.message.includes('钉钉API调用失败')) {
      const statusMatch = error.message.match(/(\d{3})/);
      const status = statusMatch ? parseInt(statusMatch[1]) : 0;
      
      switch (status) {
        case 401:
          return {
            type: DINGTALK_ERROR_TYPES.AUTH_EXPIRED,
            message: '钉钉登录已过期，请重新登录',
            suggestion: '点击下方按钮重新登录钉钉账户',
            retry: false,
            action: 'reauth',
            icon: this.getErrorIcon('auth')
          };
          
        case 403:
          return {
            type: DINGTALK_ERROR_TYPES.PERMISSION_DENIED,
            message: '权限不足，无法访问该功能',
            suggestion: '请联系管理员获取相应权限',
            retry: false,
            icon: this.getErrorIcon('permission')
          };
          
        case 429:
          return {
            type: DINGTALK_ERROR_TYPES.API_ERROR,
            message: '请求过于频繁，请稍后重试',
            suggestion: '请等待一段时间后再次尝试',
            retry: true,
            retryDelay: 5000,
            icon: this.getErrorIcon('rate-limit')
          };
          
        case 500:
        case 502:
        case 503:
          return {
            type: DINGTALK_ERROR_TYPES.API_ERROR,
            message: '钉钉服务暂时不可用',
            suggestion: '服务器正在维护，请稍后重试',
            retry: true,
            retryDelay: 10000,
            icon: this.getErrorIcon('server')
          };
          
        default:
          return {
            type: DINGTALK_ERROR_TYPES.API_ERROR,
            message: `API调用失败 (${status})`,
            suggestion: '请稍后重试或联系技术支持',
            retry: true,
            icon: this.getErrorIcon('api')
          };
      }
    }
    
    // Cookie相关错误
    if (error.message && error.message.includes('Cookie')) {
      return {
        type: DINGTALK_ERROR_TYPES.COOKIE_ERROR,
        message: '认证信息异常',
        suggestion: '请清理浏览器缓存后重新登录',
        retry: false,
        action: 'clear-cache',
        icon: this.getErrorIcon('cookie')
      };
    }
    
    // 组织未选择错误
    if (error.message && error.message.includes('组织')) {
      return {
        type: DINGTALK_ERROR_TYPES.ORG_NOT_SELECTED,
        message: '请选择要使用的组织',
        suggestion: '在设置页面中选择您要使用的钉钉组织',
        retry: false,
        action: 'select-org',
        icon: this.getErrorIcon('organization')
      };
    }

    // 存储配额错误
    if (error.message && (error.message.includes('quota') || error.message.includes('QUOTA_BYTES'))) {
      return {
        type: 'STORAGE_QUOTA_ERROR',
        message: '存储空间不足',
        suggestion: '请清理历史记录或减少保存的内容长度',
        retry: false,
        action: 'clear-storage',
        icon: this.getErrorIcon('storage')
      };
    }

    // 连接错误
    if (error.message && error.message.includes('Could not establish connection')) {
      return {
        type: 'CONNECTION_ERROR',
        message: '扩展连接失败',
        suggestion: '请刷新页面后重试，或重新启动浏览器',
        retry: true,
        icon: this.getErrorIcon('connection')
      };
    }
    
    // 通用错误
    return {
      type: DINGTALK_ERROR_TYPES.UNKNOWN_ERROR,
      message: error.message || '发生未知错误',
      suggestion: '请刷新页面后重试，如问题持续请联系技术支持',
      retry: true,
      icon: this.getErrorIcon('unknown')
    };
  }

  // 获取错误图标
  getErrorIcon(type) {
    const icons = {
      network: `<path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>`,
      
      auth: `<path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
             <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2"/>`,
      
      permission: `<path d="M12 1L3 5V11C3 16 6 20 12 23C18 20 21 16 21 11V5L12 1Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                   <path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>`,
      
      'rate-limit': `<circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                     <polyline points="12,6 12,12 16,14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>`,
      
      server: `<rect x="2" y="3" width="20" height="14" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
               <line x1="8" y1="21" x2="16" y2="21" stroke="currentColor" stroke-width="2"/>
               <line x1="12" y1="17" x2="12" y2="21" stroke="currentColor" stroke-width="2"/>`,
      
      api: `<polygon points="12 2 2 7 12 12 22 7 12 2" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
            <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
            <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>`,
      
      cookie: `<circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
               <path d="M12 1V3" stroke="currentColor" stroke-width="2"/>
               <path d="M12 21V23" stroke="currentColor" stroke-width="2"/>
               <path d="M4.22 4.22L5.64 5.64" stroke="currentColor" stroke-width="2"/>
               <path d="M18.36 18.36L19.78 19.78" stroke="currentColor" stroke-width="2"/>`,
      
      organization: `<path d="M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21" stroke="currentColor" stroke-width="2"/>
                     <circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                     <path d="M23 21V19C23 18.1645 22.7155 17.3541 22.2094 16.6977C21.7033 16.0414 20.9983 15.5735 20.2 15.3613" stroke="currentColor" stroke-width="2"/>`,
      
      unknown: `<circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                <line x1="12" y1="8" x2="12" y2="12" stroke="currentColor" stroke-width="2"/>
                <line x1="12" y1="16" x2="12.01" y2="16" stroke="currentColor" stroke-width="2"/>`
    };
    
    return icons[type] || icons.unknown;
  }

  // 记录错误历史
  recordError(error) {
    const errorRecord = {
      timestamp: new Date().toISOString(),
      message: error.message,
      stack: error.stack,
      type: error.name || 'Error'
    };
    
    this.errorHistory.unshift(errorRecord);
    
    // 限制历史记录大小
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory = this.errorHistory.slice(0, this.maxHistorySize);
    }
  }

  // 处理认证错误
  async handleAuthError(error, context = {}) {
    const errorInfo = this.analyzeError(error);
    const traceId = context.traceId || `error_${Date.now()}`;
    
    console.error(`[${traceId}] 钉钉认证错误:`, errorInfo);
    
    // 根据错误类型执行相应的处理
    switch (errorInfo.type) {
      case DINGTALK_ERROR_TYPES.AUTH_EXPIRED:
        return this.handleAuthExpired(errorInfo, context, traceId);
        
      case DINGTALK_ERROR_TYPES.PERMISSION_DENIED:
        return this.handlePermissionDenied(errorInfo, context, traceId);
        
      case DINGTALK_ERROR_TYPES.ORG_NOT_SELECTED:
        return this.handleOrgNotSelected(errorInfo, context, traceId);
        
      case DINGTALK_ERROR_TYPES.NETWORK_ERROR:
        return this.handleNetworkError(errorInfo, context, traceId);
        
      default:
        return this.handleGenericError(errorInfo, context, traceId);
    }
  }

  // 处理认证过期
  async handleAuthExpired(errorInfo, context, traceId) {
    console.log(`[${traceId}] 处理认证过期`);
    
    // 清理认证状态
    if (context.authManager) {
      await context.authManager.clearAuthState();
    }
    
    // 发送消息给UI
    if (context.tabId) {
      await this.sendTabMessage(context.tabId, DINGTALK_MESSAGE_TYPES.DT_LOGIN_REQUIRED, {
        errorInfo: errorInfo,
        traceId: traceId
      });
    }
    
    return {
      handled: true,
      action: 'login_required',
      errorInfo: errorInfo
    };
  }

  // 处理权限不足
  async handlePermissionDenied(errorInfo, context, traceId) {
    console.log(`[${traceId}] 处理权限不足`);
    
    if (context.tabId) {
      await this.sendTabMessage(context.tabId, DINGTALK_MESSAGE_TYPES.DT_PERMISSION_ERROR, {
        errorInfo: errorInfo,
        traceId: traceId
      });
    }
    
    return {
      handled: true,
      action: 'permission_denied',
      errorInfo: errorInfo
    };
  }

  // 处理组织未选择
  async handleOrgNotSelected(errorInfo, context, traceId) {
    console.log(`[${traceId}] 处理组织未选择`);
    
    if (context.tabId) {
      await this.sendTabMessage(context.tabId, DINGTALK_MESSAGE_TYPES.DT_ORG_SELECTION_REQUIRED, {
        errorInfo: errorInfo,
        traceId: traceId
      });
    }
    
    return {
      handled: true,
      action: 'org_selection_required',
      errorInfo: errorInfo
    };
  }

  // 处理网络错误
  async handleNetworkError(errorInfo, context, traceId) {
    console.log(`[${traceId}] 处理网络错误`);
    
    if (context.tabId) {
      await this.sendTabMessage(context.tabId, DINGTALK_MESSAGE_TYPES.DT_NETWORK_ERROR, {
        errorInfo: errorInfo,
        traceId: traceId,
        canRetry: errorInfo.retry
      });
    }
    
    return {
      handled: true,
      action: 'network_error',
      errorInfo: errorInfo
    };
  }

  // 处理通用错误
  async handleGenericError(errorInfo, context, traceId) {
    console.log(`[${traceId}] 处理通用错误`);
    
    if (context.tabId) {
      await this.sendTabMessage(context.tabId, DINGTALK_MESSAGE_TYPES.DT_AUTH_ERROR, {
        errorInfo: errorInfo,
        traceId: traceId,
        canRetry: errorInfo.retry
      });
    }
    
    return {
      handled: true,
      action: 'generic_error',
      errorInfo: errorInfo
    };
  }

  // 发送消息给标签页
  async sendTabMessage(tabId, messageType, data) {
    try {
      await chrome.tabs.sendMessage(tabId, {
        type: messageType,
        data: data
      });
      console.log(`消息已发送给标签页 ${tabId}:`, messageType);
    } catch (error) {
      console.error(`发送消息给标签页 ${tabId} 失败:`, error);
    }
  }

  // 重试机制
  async retryWithBackoff(fn, maxRetries = 3, baseDelay = 1000) {
    let lastError;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        
        if (attempt === maxRetries) {
          break;
        }
        
        const delay = baseDelay * Math.pow(2, attempt - 1);
        console.log(`重试第 ${attempt} 次，${delay}ms 后重试`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError;
  }

  // 获取错误历史
  getErrorHistory() {
    return [...this.errorHistory];
  }

  // 清理错误历史
  clearErrorHistory() {
    this.errorHistory = [];
  }

  // 生成错误报告
  generateErrorReport() {
    return {
      timestamp: new Date().toISOString(),
      totalErrors: this.errorHistory.length,
      recentErrors: this.errorHistory.slice(0, 10),
      errorTypes: this.getErrorTypeStats()
    };
  }

  // 获取错误类型统计
  getErrorTypeStats() {
    const stats = {};
    this.errorHistory.forEach(error => {
      const type = error.type || 'Unknown';
      stats[type] = (stats[type] || 0) + 1;
    });
    return stats;
  }
}

// 导出
if (typeof window !== 'undefined') {
  window.DingTalkErrorHandler = DingTalkErrorHandler;
  window.DINGTALK_ERROR_TYPES = DINGTALK_ERROR_TYPES;
  window.DINGTALK_MESSAGE_TYPES = DINGTALK_MESSAGE_TYPES;
}
